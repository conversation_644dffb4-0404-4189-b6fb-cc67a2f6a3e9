[build]
  # Build command for static export - Skip prepare scripts to avoid husky issues
  command = "npm ci --include=dev --ignore-scripts && npm run postinstall && npm run build:netlify"

  # Output directory for static files
  publish = "out"

  # Functions directory for serverless functions
  functions = "netlify/functions"

  # Environment variables for build
  environment = { NODE_VERSION = "18", NEXT_TELEMETRY_DISABLED = "1", NETLIFY = "true", HUSKY = "0" }

[build.processing]
  # Skip processing of certain file types for faster builds
  skip_processing = false

[build.processing.css]
  # Enable CSS optimization
  bundle = true
  minify = true

[build.processing.js]
  # Enable JavaScript optimization
  bundle = true
  minify = true

[build.processing.html]
  # Enable HTML optimization
  pretty_urls = true

[[redirects]]
  from = "/home"
  to = "/"
  status = 301

[[redirects]]
  from = "/health"
  to = "/api/health"
  status = 200

[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/:splat"
  status = 200
  force = true

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Permissions-Policy = "camera=(), microphone=(), geolocation=()"

[[headers]]
  for = "/api/*"
  [headers.values]
    Access-Control-Allow-Origin = "*"
    Access-Control-Allow-Methods = "GET, POST, PUT, DELETE, OPTIONS"
    Access-Control-Allow-Headers = "Content-Type, Authorization"
    Cache-Control = "no-store, max-age=0"

# Specific headers for HTML files
[[headers]]
  for = "/*.html"
  [headers.values]
    Cache-Control = "public, max-age=0, must-revalidate"

# Performance headers for static assets
[[headers]]
  for = "/static/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[functions]
  directory = "netlify/functions"
  node_bundler = "esbuild"

# No plugins needed for static export

# Environment-specific configurations
[context.production]
  command = "npm ci --include=dev --ignore-scripts && npm run postinstall && npm run build:netlify"

  [context.production.environment]
    NODE_ENV = "production"
    NETLIFY = "true"
    HUSKY = "0"
    NEXT_PUBLIC_SITE_URL = "https://ghostlayer.netlify.app"

[context.deploy-preview]
  command = "npm ci --include=dev --ignore-scripts && npm run postinstall && npm run build:netlify"

  [context.deploy-preview.environment]
    NODE_ENV = "production"
    NETLIFY = "true"
    HUSKY = "0"
    NEXT_PUBLIC_SITE_URL = "https://deploy-preview-$REVIEW_ID--ghostlayer.netlify.app"

[context.branch-deploy]
  command = "npm ci --include=dev --ignore-scripts && npm run postinstall && npm run build:netlify"

  [context.branch-deploy.environment]
    NODE_ENV = "production"
    NETLIFY = "true"
    HUSKY = "0"
    NEXT_PUBLIC_SITE_URL = "https://$BRANCH--ghostlayer.netlify.app"

# Plugin configurations
[[plugins]]
  package = "netlify-plugin-submit-sitemap"

  [plugins.inputs]
    baseUrl = "https://ghostlayer.netlify.app"
    sitemapPath = "/sitemap.xml"
    ignorePeriod = 0
    providers = [
      "google",
      "bing",
      "yandex"
    ]

[dev]
  command = "npm run dev"
  port = 3000
  publish = "out"
